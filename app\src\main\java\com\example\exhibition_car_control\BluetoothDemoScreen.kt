package com.example.exhibition_car_control

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.content.Intent
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import com.zerosense.bluetooth.*
import com.example.exhibition_car_control.config.DeviceConfig
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun BluetoothDemoScreen(modifier: Modifier = Modifier) {
    val context = LocalContext.current
    
    // SDK 初始化状态
    var sdkInitialized by remember { mutableStateOf(false) }
    var bluetoothManager by remember { mutableStateOf<BluetoothManager?>(null) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var successMessage by remember { mutableStateOf<String?>(null) }
    var infoMessage by remember { mutableStateOf<String?>(null) }

    // 初始化 SDK
    LaunchedEffect(Unit) {
        try {
            ZeroSenseBluetoothSDK.initialize(context)
            bluetoothManager = ZeroSenseBluetoothSDK.getBluetoothManager()
            sdkInitialized = true
        } catch (e: Exception) {
            errorMessage = "SDK初始化失败: ${e.message}"
            sdkInitialized = true // 即使失败也标记为已尝试初始化
        }
    }

    // 状态管理
    var scanState by remember { mutableStateOf(BluetoothScanState.IDLE) }
    var discoveredDevices by remember { mutableStateOf<List<BluetoothDeviceInfo>>(emptyList()) }
    var pairedDevices by remember { mutableStateOf<List<BluetoothDeviceInfo>>(emptyList()) }
    var connectedDevices by remember { mutableStateOf<List<BluetoothDeviceInfo>>(emptyList()) }
    var deviceConnectionStates by remember { mutableStateOf<Map<String, BluetoothConnectionState>>(emptyMap()) }

    // 服务器相关状态
    var isServerRunning by remember { mutableStateOf(false) }
    var isUnifiedServer by remember { mutableStateOf(false) } // 是否使用统一服务器
    var receivedMessages by remember { mutableStateOf<List<ReceivedMessage>>(emptyList()) }

    // 树莓派控制相关状态
    var raspberryPiDevices by remember { mutableStateOf<List<String>>(emptyList()) }
    var peripheralStates by remember { mutableStateOf<Map<String, Boolean>>(emptyMap()) }
    var scannedDevices by remember { mutableStateOf<List<String>>(emptyList()) }
    var localScannedDevices by remember { mutableStateOf<List<String>>(emptyList()) } // 本地BLE扫描结果
    var remoteScannedDevices by remember { mutableStateOf<List<String>>(emptyList()) } // 远程树莓派扫描结果
    var keyBindings by remember { mutableStateOf<Map<Int, String>>(emptyMap()) }

    // 扫描状态管理
    var isScanning by remember { mutableStateOf(false) }
    var scanStatusMessage by remember { mutableStateOf("") }
    
    // 权限管理
    val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        arrayOf(
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
    } else {
        arrayOf(
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
    }
    
    val permissionsState = rememberMultiplePermissionsState(permissions.toList())
    
    // 蓝牙启用请求
    val enableBluetoothLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        // 处理蓝牙启用结果
    }
    
    // SDK 回调
    val callback = remember {
        object : BluetoothCallback {
            override fun onScanStateChanged(state: BluetoothScanState) {
                scanState = state
            }
            
            override fun onDeviceFound(device: BluetoothDeviceInfo) {
                // 只有未配对的设备才添加到已发现设备列表
                if (!device.isPaired) {
                    discoveredDevices = discoveredDevices.toMutableList().apply {
                        removeAll { it.address == device.address }
                        add(device)
                    }
                }

                // 注意：手机版本不应该将本地扫描到的设备添加到树莓派控制中心的扫描列表
                // 树莓派控制中心的扫描列表应该只显示树莓派发送过来的扫描结果
                // 这段代码只在树莓派版本中执行，用于树莓派本地扫描
                if (DeviceConfig.isRaspberryPiVersion && device.name != null) {
                    // 判断是否是目标外设类型
                    val name = device.name!!.lowercase()
                    val isTargetDevice = name.contains("charger") ||
                                       name.contains("lamp") ||
                                       name.contains("light") ||
                                       name.contains("aroma") ||
                                       name.contains("fan") ||
                                       name.contains("dotix") ||
                                       name.contains("无线充电") ||
                                       name.contains("主灯") ||
                                       name.contains("氛围灯") ||
                                       name.contains("香氛") ||
                                       name.contains("风扇") ||
                                       name.contains("点云") ||
                                       name.contains("灵动键") ||
                                       name.contains("按键") ||
                                       name.contains("ble") ||
                                       name.contains("test") ||
                                       name.contains("demo") ||
                                       name.contains("device") ||
                                       name.isNotEmpty() // 暂时允许所有有名称的设备

                    if (isTargetDevice) {
                        // 确定设备类型
                        val deviceType = when {
                            name.contains("dotix") || name.contains("点云") || name.contains("灵动键") || name.contains("按键") -> "01"
                            name.contains("charger") || name.contains("无线充电") -> "02"
                            name.contains("lamp") || name.contains("主灯") -> "03"
                            name.contains("light") || name.contains("氛围灯") -> "04"
                            name.contains("aroma") || name.contains("香氛") -> "05"
                            name.contains("fan") || name.contains("风扇") -> "06"
                            else -> "01" // 默认为点云灵动键类型
                        }

                        // 只有在树莓派版本中才添加到本地扫描结果列表
                        // 这是为了树莓派本地显示扫描到的外设设备
                        val formattedDevice = "$deviceType|${device.name}|${device.address}"
                        println("🔍 本地BLE扫描到设备: $formattedDevice")
                        localScannedDevices = localScannedDevices.toMutableList().apply {
                            // 避免重复添加
                            if (!any { it.contains(device.address) }) {
                                add(formattedDevice)
                                println("🔍 本地BLE设备已添加到本地扫描列表: ${device.name}")
                            } else {
                                println("🔍 本地BLE设备已存在，跳过: ${device.name}")
                            }
                        }
                        println("🔍 本地BLE扫描后，localScannedDevices.size = ${localScannedDevices.size}")

                        // 如果当前是树莓派版本，也更新主扫描列表（用于显示）
                        scannedDevices = localScannedDevices
                        println("🔍 同步到主扫描列表，scannedDevices.size = ${scannedDevices.size}")
                    }
                }
            }
            
            override fun onConnectionStateChanged(device: BluetoothDeviceInfo, state: BluetoothConnectionState) {
                // 更新连接状态
                deviceConnectionStates = deviceConnectionStates.toMutableMap().apply {
                    this[device.address] = state
                }

                when (state) {
                    BluetoothConnectionState.CONNECTED -> {
                        // 设备配对成功，更新各个列表
                        val updatedDevice = device.copy(isConnected = true, isPaired = true)

                        // 添加到已配对设备列表（也就是已连接设备列表）
                        pairedDevices = pairedDevices.toMutableList().apply {
                            removeAll { it.address == device.address }
                            add(updatedDevice)
                        }

                        // 从已发现设备列表中移除
                        discoveredDevices = discoveredDevices.filter { it.address != device.address }

                        // 更新已连接设备列表（实际上就是已配对设备）
                        connectedDevices = pairedDevices
                    }
                    BluetoothConnectionState.DISCONNECTED -> {
                        // 设备断开连接（取消配对或连接失败）
                        pairedDevices = pairedDevices.filter { it.address != device.address }
                        connectedDevices = pairedDevices
                    }
                    else -> {
                        // 处理其他连接状态（如正在连接）
                    }
                }
            }
            
            override fun onError(error: String) {
                errorMessage = error
                successMessage = null // 清除成功消息
                infoMessage = null // 清除信息提示
            }

            override fun onSuccess(message: String) {
                successMessage = message
                errorMessage = null // 清除错误消息
                infoMessage = null // 清除信息提示
            }

            override fun onInfo(message: String) {
                infoMessage = message
                errorMessage = null // 清除错误消息
                successMessage = null // 清除成功消息
            }

            override fun onPermissionRequired(permissions: Array<String>) {
                // 权限已通过 Accompanist 处理
            }

            override fun onDataReceived(senderAddress: String, data: String) {
                // 添加接收到的消息
                receivedMessages = receivedMessages + ReceivedMessage(senderAddress, data)

                // 调试信息：显示接收到的所有数据
                println("📨 收到蓝牙数据: 发送者=$senderAddress, 数据=$data")

                // 处理蓝牙按键控制
                processBluetoothKeyControl(
                    senderAddress,
                    data,
                    bluetoothManager!!,
                    keyBindings,
                    raspberryPiDevices,
                    peripheralStates
                ) { newStates ->
                    peripheralStates = newStates
                }

                // 处理树莓派响应
                processRaspberryPiResponse(
                    data,
                    { info -> infoMessage = info },
                    scannedDevices,
                    raspberryPiDevices,
                    peripheralStates,
                    remoteScannedDevices,
                    { newDevices ->
                        println("🔄 [主回调] 收到扫描设备更新，设备数量: ${newDevices.size}")
                        newDevices.forEachIndexed { index, device ->
                            println("🔄 [主回调] 设备 $index: $device")
                        }
                        scannedDevices = newDevices
                        println("🔄 [主回调] scannedDevices 状态已更新，当前大小: ${scannedDevices.size}")
                    },
                    { raspberryPiDevices = it },
                    { peripheralStates = it },
                    { remoteScannedDevices = it },
                    { scanning, message ->
                        isScanning = scanning
                        scanStatusMessage = message
                    }
                )
            }

            override fun onCharacteristicNotification(deviceAddress: String, characteristicUuid: String, data: ByteArray) {
                // 处理BLE特征值通知
                val dataString = data.joinToString(" ") { "%02X".format(it) }
                val message = "BLE通知 [${characteristicUuid}]: $dataString"
                receivedMessages = receivedMessages + ReceivedMessage(deviceAddress, message)
            }

            override fun onBleConnectionStateChanged(deviceAddress: String, state: Int) {
                // 处理BLE连接状态变化
                val stateString = when (state) {
                    2 -> "已连接" // BluetoothProfile.STATE_CONNECTED
                    0 -> "已断开" // BluetoothProfile.STATE_DISCONNECTED
                    1 -> "正在连接" // BluetoothProfile.STATE_CONNECTING
                    3 -> "正在断开" // BluetoothProfile.STATE_DISCONNECTING
                    else -> "未知状态($state)"
                }
                infoMessage = "BLE设备 $deviceAddress $stateString"
            }
        }
    }
    
    // 注册回调并初始化已配对设备
    LaunchedEffect(bluetoothManager) {
        bluetoothManager?.addCallback(callback)
        // 初始化已配对设备列表
        bluetoothManager?.let { manager ->
            pairedDevices = manager.getPairedDevices().map { device ->
                device.copy(isConnected = true) // 已配对设备视为已连接
            }
            connectedDevices = pairedDevices
            // 初始化服务器状态
            isServerRunning = manager.isServerRunning()

            // 树莓派版本自动启动服务器
            if (DeviceConfig.Features.autoStartServer && !isServerRunning) {
                if (manager.startUnifiedBluetoothServer()) {
                    isServerRunning = true
                    isUnifiedServer = true
                    successMessage = "树莓派版本已自动启动统一蓝牙服务器"
                }
            }
        }
    }
    
    // 清理回调
    DisposableEffect(bluetoothManager) {
        onDispose {
            bluetoothManager?.removeCallback(callback)
        }
    }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        item {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = DeviceConfig.UI.mainTitle,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(DeviceConfig.UI.primaryColor)
                )
                Text(
                    text = DeviceConfig.UI.subtitle,
                    fontSize = 16.sp,
                    color = Color.Gray
                )

                // 调试信息（仅在调试模式下显示）
                if (BuildConfig.DEBUG) {
                    Card(
                        colors = CardDefaults.cardColors(containerColor = Color.Yellow.copy(alpha = 0.1f))
                    ) {
                        Text(
                            text = "调试信息:\n设备类型: ${DeviceConfig.currentDeviceType.name}",
                            modifier = Modifier.padding(8.dp),
                            fontSize = 12.sp,
                            color = Color(0xFF795548)
                        )
                    }
                }
            }
        }

        // 错误信息显示
        errorMessage?.let { error ->
            item {
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color.Red.copy(alpha = 0.1f))
                ) {
                    Text(
                        text = "错误: $error",
                        modifier = Modifier.padding(12.dp),
                        color = Color.Red
                    )
                }
            }
        }

        // 成功信息显示
        successMessage?.let { success ->
            item {
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color.Green.copy(alpha = 0.1f))
                ) {
                    Text(
                        text = "✓ $success",
                        modifier = Modifier.padding(12.dp),
                        color = Color(0xFF006400) // 深绿色
                    )
                }
            }
        }

        // 信息提示显示
        infoMessage?.let { info ->
            item {
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color.Blue.copy(alpha = 0.1f))
                ) {
                    Text(
                        text = "ℹ $info",
                        modifier = Modifier.padding(12.dp),
                        color = Color(0xFF1976D2) // 蓝色
                    )
                }
            }
        }

        // 权限检查
        if (!permissionsState.allPermissionsGranted) {
            item {
                Card {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text("需要蓝牙权限才能使用此功能")
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(
                            onClick = { permissionsState.launchMultiplePermissionRequest() }
                        ) {
                            Text("请求权限")
                        }
                    }
                }
            }
        } else {
            // SDK 初始化检查
            if (!sdkInitialized) {
                item {
                    Card {
                        Column(modifier = Modifier.padding(16.dp)) {
                            Text("正在初始化蓝牙SDK...")
                            Spacer(modifier = Modifier.height(8.dp))
                            CircularProgressIndicator()
                        }
                    }
                }
            } else if (bluetoothManager == null) {
                item {
                    Card {
                        Text(
                            text = "蓝牙SDK初始化失败",
                            modifier = Modifier.padding(16.dp),
                            color = Color.Red
                        )
                    }
                }
            } else {
                val manager = bluetoothManager
                if (manager == null || !manager.isBluetoothAvailable()) {
                    item {
                        Card {
                            Text(
                                text = "设备不支持蓝牙",
                                modifier = Modifier.padding(16.dp)
                            )
                        }
                    }
                } else if (!manager.isBluetoothEnabled()) {
                    item {
                        Card {
                            Column(modifier = Modifier.padding(16.dp)) {
                                Text("蓝牙未启用")
                                Spacer(modifier = Modifier.height(8.dp))
                                Button(
                                    onClick = {
                                        val enableBtIntent =
                                            Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                        enableBluetoothLauncher.launch(enableBtIntent)
                                    }
                                ) {
                                    Text("启用蓝牙")
                                }
                            }
                        }
                    }
                } else {
                    // 树莓派控制部分（只在手机版显示，放在最上方方便操作）
                    if (DeviceConfig.Features.showRaspberryPiControl) {
                        item {
                            RaspberryPiControlSection(
                                bluetoothManager = manager,
                                connectedDevices = connectedDevices,
                                scannedDevices = scannedDevices,
                                remoteScannedDevices = remoteScannedDevices,
                                raspberryPiDevices = raspberryPiDevices,
                                peripheralStates = peripheralStates,
                                keyBindings = keyBindings,
                                isScanning = isScanning,
                                scanStatusMessage = scanStatusMessage,
                                onScannedDevicesUpdate = { newDevices ->
                                    println("🔄 状态更新回调被调用，新设备列表大小: ${newDevices.size}")
                                    newDevices.forEachIndexed { index, device ->
                                        println("🔄 设备 $index: $device")
                                    }
                                    scannedDevices = newDevices
                                    println("🔄 scannedDevices 状态已更新，当前大小: ${scannedDevices.size}")
                                },
                                onRemoteScannedDevicesUpdate = { newDevices ->
                                    println("🔄 [主回调] 远程扫描设备更新回调被调用，设备数量: ${newDevices.size}")
                                    newDevices.forEachIndexed { index, device ->
                                        println("🔄 [主回调] 远程设备 $index: $device")
                                    }
                                    remoteScannedDevices = newDevices
                                    println("🔄 [主回调] remoteScannedDevices 状态已更新，当前大小: ${remoteScannedDevices.size}")
                                },
                                onKeyBindingsUpdate = { keyBindings = it },
                                onScanStatusUpdate = { scanning, message ->
                                    isScanning = scanning
                                    scanStatusMessage = message
                                }
                            )
                        }
                    }

                    // 蓝牙服务器部分（仅在树莓派版本显示）
                    if (DeviceConfig.Features.showBluetoothServer) {
                        item {
                            UnifiedBluetoothServerSection(
                                bluetoothManager = manager,
                                isServerRunning = isServerRunning,
                                isUnifiedServer = isUnifiedServer,
                                receivedMessages = receivedMessages,
                                onStartClassicServer = {
                                    if (manager.startBluetoothServer()) {
                                        isServerRunning = true
                                        isUnifiedServer = false
                                    }
                                },
                                onStartUnifiedServer = {
                                    if (manager.startUnifiedBluetoothServer()) {
                                        isServerRunning = true
                                        isUnifiedServer = true
                                    }
                                },
                                onStopServer = {
                                    if (isUnifiedServer) {
                                        manager.stopUnifiedBluetoothServer()
                                    } else {
                                        manager.stopBluetoothServer()
                                    }
                                    isServerRunning = false
                                    isUnifiedServer = false
                                },
                                onClearMessages = {
                                    receivedMessages = emptyList()
                                }
                            )
                        }
                    }

                    // 蓝牙客户端部分（两个版本都显示，用于连接BLE按键设备）
                    if (DeviceConfig.Features.showBluetoothClient) {
                        item {
                            BluetoothControlSection(
                                bluetoothManager = manager,
                                scanState = scanState,
                                discoveredDevices = discoveredDevices,
                                pairedDevices = pairedDevices,
                                onRefreshPairedDevices = {
                                    pairedDevices = manager.getPairedDevices().map { device ->
                                        device.copy(isConnected = true) // 已配对设备视为已连接
                                    }
                                    connectedDevices = pairedDevices
                                },
                                deviceConnectionStates = deviceConnectionStates
                            )
                        }
                    }

                    // BLE功能部分（只在树莓派版显示）
                    if (DeviceConfig.Features.showBleFeatures) {
                        item {
                            BleControlSection(
                                bluetoothManager = manager
                            )
                        }
                    }
                }
            }
        }
    }
}

// 状态管理已移至组件内部

// 处理蓝牙按键控制
private fun processBluetoothKeyControl(
    senderAddress: String,
    data: String,
    bluetoothManager: BluetoothManager,
    keyBindings: Map<Int, String>,
    raspberryPiDevices: List<String>,
    peripheralStates: Map<String, Boolean>,
    onPeripheralStatesUpdate: (Map<String, Boolean>) -> Unit
) {
    try {
        // 检查是否是按键数据（格式：按键编号，如 "1", "2", "3", "4", "5", "6"）
        val keyNumber = data.trim().toIntOrNull()
        if (keyNumber != null && keyNumber in 1..6) {
            // 查找绑定的设备
            val boundDeviceAddress = keyBindings[keyNumber]
            if (boundDeviceAddress != null) {
                // 查找对应的树莓派设备
                val raspberryPiDevice = raspberryPiDevices.find { it.contains(boundDeviceAddress) }
                if (raspberryPiDevice != null) {
                    // 获取设备类型
                    val parts = raspberryPiDevice.split("|")
                    if (parts.size >= 3) {
                        val deviceType = parts[0]
                        val deviceName = parts[1]

                        // 获取当前设备状态
                        val currentState = peripheralStates[boundDeviceAddress] ?: false
                        val newState = !currentState // 切换状态
                        val command = if (newState) "01" else "00" // 01=开启, 00=关闭

                        // 发送控制指令到树莓派
                        val controlCommand = "A1:$deviceType:$command:$boundDeviceAddress"

                        // 找到连接的树莓派设备并发送指令
                        // 这里假设只有一个树莓派连接，实际应用中可能需要更复杂的逻辑
                        val connectedRaspberryPi = raspberryPiDevices.firstOrNull()
                        if (connectedRaspberryPi != null) {
                            // 从已连接设备中找到树莓派的地址
                            // 这里需要一个方法来获取树莓派的蓝牙地址
                            // 暂时使用发送者地址作为目标（假设按键设备连接到同一个树莓派）
                            bluetoothManager.sendData(senderAddress, controlCommand)

                            // 更新本地状态（乐观更新）
                            onPeripheralStatesUpdate(peripheralStates.toMutableMap().apply {
                                this[boundDeviceAddress] = newState
                            })
                        }
                    }
                }
            }
        }
    } catch (e: Exception) {
        // 忽略解析错误，可能不是按键数据
    }
}

// 处理树莓派响应
private fun processRaspberryPiResponse(
    data: String,
    onInfo: (String) -> Unit,
    scannedDevices: List<String>,
    raspberryPiDevices: List<String>,
    peripheralStates: Map<String, Boolean>,
    remoteScannedDevices: List<String>,
    onScannedDevicesUpdate: (List<String>) -> Unit,
    onRaspberryPiDevicesUpdate: (List<String>) -> Unit,
    onPeripheralStatesUpdate: (Map<String, Boolean>) -> Unit,
    onRemoteScannedDevicesUpdate: (List<String>) -> Unit,
    onScanStatusUpdate: (Boolean, String) -> Unit
) {
    try {
        // 清理数据：移除可能的换行符和空白字符
        val cleanData = data.trim()

        // 添加调试信息
        println("📨 收到树莓派原始响应: '$data'")
        println("📨 清理后的响应: '$cleanData'")
        onInfo("收到树莓派响应: $cleanData")

        // 尝试解析JSON格式
        if (cleanData.startsWith("{")) {
            processJsonResponse(cleanData, onInfo, onScannedDevicesUpdate, onRaspberryPiDevicesUpdate, onPeripheralStatesUpdate, onRemoteScannedDevicesUpdate)
            return
        }

        // 传统格式处理
        val parts = cleanData.split(":")
        if (parts.size < 2) {
            println("⚠️ 响应格式不正确，部分数量: ${parts.size}")
            return
        }

        println("📨 解析响应部分: ${parts.joinToString(" | ")}")

        when (parts[0]) {
            "A0" -> { // 树莓派管理响应
                when (parts[1]) {
                    "SCAN_RESULT" -> { // 扫描结果
                        println("🔍 收到树莓派扫描结果: $cleanData")
                        println("🔍 parts.size = ${parts.size}")
                        parts.forEachIndexed { index, part ->
                            println("🔍 parts[$index] = '$part'")
                        }

                        if (parts.size >= 3) {
                            // 解析扫描到的设备信息: 设备类型|设备名|设备地址|信号强度
                            val deviceInfo = parts[2]
                            println("🔍 解析设备信息: '$deviceInfo'")
                            val deviceParts = deviceInfo.split("|")
                            println("🔍 deviceParts.size = ${deviceParts.size}")
                            deviceParts.forEachIndexed { index, part ->
                                println("🔍 deviceParts[$index] = '$part'")
                            }

                            if (deviceParts.size >= 3) {
                                val deviceType = deviceParts[0]
                                val deviceName = deviceParts[1]
                                val deviceAddress = deviceParts[2]
                                val rssi = if (deviceParts.size >= 4) deviceParts[3] else "未知"

                                println("🔍 解析出设备: 类型=$deviceType, 名称=$deviceName, 地址=$deviceAddress, 信号强度=$rssi")

                                // 添加到远程扫描结果列表
                                val formattedDevice = "$deviceType|$deviceName|$deviceAddress"
                                println("🔍 处理远程扫描结果: $formattedDevice")
                                println("🔍 当前 remoteScannedDevices 大小: ${remoteScannedDevices.size}")
                                remoteScannedDevices.forEachIndexed { index, device ->
                                    println("🔍 当前 remoteScannedDevices[$index] = '$device'")
                                }

                                // 检查是否已存在该设备
                                val deviceExists = remoteScannedDevices.any { it.contains(deviceAddress) }
                                println("🔍 设备是否已存在: $deviceExists")

                                if (!deviceExists) {
                                    val updatedRemoteScannedDevices = remoteScannedDevices.toMutableList().apply {
                                        add(formattedDevice)
                                    }

                                    println("🔍 添加新设备后列表大小: ${updatedRemoteScannedDevices.size}")
                                    onInfo("✅ 添加远程扫描设备: $deviceName ($deviceAddress), 当前列表大小: ${updatedRemoteScannedDevices.size}")
                                    println("✅ 成功添加远程扫描设备到列表: $deviceName ($deviceAddress)")

                                    // 更新远程扫描结果状态
                                    println("🔄 准备调用 onRemoteScannedDevicesUpdate，列表大小: ${updatedRemoteScannedDevices.size}")
                                    onRemoteScannedDevicesUpdate(updatedRemoteScannedDevices)
                                    println("🔄 onRemoteScannedDevicesUpdate 调用完成")

                                    // 通过回调更新主扫描列表（用于UI显示）
                                    println("🔄 准备调用 onScannedDevicesUpdate，列表大小: ${updatedRemoteScannedDevices.size}")
                                    onScannedDevicesUpdate(updatedRemoteScannedDevices)
                                    println("🔄 onScannedDevicesUpdate 调用完成")
                                } else {
                                    onInfo("⚠️ 远程设备已存在，跳过: $deviceName ($deviceAddress)")
                                    println("⚠️ 远程设备已存在，跳过: $deviceName ($deviceAddress)")
                                }

                                println("🔄 ===== 扫描结果处理完成 =====")
                            } else {
                                println("❌ deviceParts.size < 3, 无法解析设备信息")
                                onInfo("❌ 设备信息格式错误: $deviceInfo")
                            }
                        } else {
                            println("❌ parts.size < 3, 无法获取设备信息")
                            onInfo("❌ 扫描结果格式错误: $cleanData")
                        }
                    }
                    "CONNECT" -> { // 连接响应
                        if (parts.size >= 4) {
                            val deviceAddress = parts[2]
                            val result = parts[3]
                            when (result) {
                                "RECEIVED" -> {
                                    // 树莓派已收到连接指令，正在处理
                                    // 可以在这里显示连接中的状态
                                }
                                "OK" -> {
                                    // 连接成功，从扫描列表移除，添加到已连接列表
                                    val connectedDevice = scannedDevices.find { it.contains(deviceAddress) }
                                    if (connectedDevice != null) {
                                        onScannedDevicesUpdate(scannedDevices.toMutableList().apply {
                                            remove(connectedDevice)
                                        })
                                        onRaspberryPiDevicesUpdate(raspberryPiDevices.toMutableList().apply {
                                            if (!any { it.contains(deviceAddress) }) {
                                                add(connectedDevice)
                                            }
                                        })
                                    }
                                }
                                "FAIL" -> {
                                    // 连接失败，可以在这里显示错误信息
                                    // 设备仍保留在扫描列表中
                                }
                            }
                        }
                    }
                    "SCAN_FINISHED" -> { // 扫描完成
                        println("🔍 树莓派扫描完成")
                        onInfo("✅ 树莓派扫描完成")
                    }
                    "01" -> { // 开始扫描响应
                        println("🔍 树莓派确认开始扫描")
                        onInfo("🔍 树莓派开始扫描")
                        onScanStatusUpdate(true, "扫描中...")

                        // 清空之前的远程扫描结果，开始新的扫描
                        println("🔄 清空之前的远程扫描结果")
                        onRemoteScannedDevicesUpdate(emptyList())
                        onScannedDevicesUpdate(emptyList())
                    }
                    "00" -> { // 停止扫描响应
                        println("🔍 树莓派确认停止扫描")
                        onInfo("🛑 树莓派停止扫描")
                        onScanStatusUpdate(false, "扫描已停止")
                    }
                }
            }
            "A2" -> { // 状态查询响应
                if (parts[1] == "00" && parts.size >= 3) {
                    // 设备列表响应
                    val deviceList = if (parts[2].isNotEmpty()) {
                        parts[2].split(";")
                    } else {
                        emptyList()
                    }
                    onRaspberryPiDevicesUpdate(deviceList)
                }
            }
            "A1" -> { // 外设控制响应
                if (parts.size >= 5) {
                    val deviceAddress = parts[3]
                    val result = parts[4]
                    if (result == "OK") {
                        // 更新设备状态
                        val command = parts[2]
                        val isOn = command == "01"
                        onPeripheralStatesUpdate(peripheralStates.toMutableMap().apply {
                            this[deviceAddress] = isOn
                        })
                    }
                }
            }
            else -> {
                println("⚠️ 未知响应类型: ${parts[0]}")
                onInfo("⚠️ 未知响应类型: ${parts[0]}")
            }
        }
    } catch (e: Exception) {
        println("❌ 解析树莓派响应时出错: ${e.message}")
        println("❌ 原始数据: '$data'")
        onInfo("❌ 解析响应失败: ${e.message}")
    }
}

// 处理JSON格式的树莓派响应
private fun processJsonResponse(
    jsonData: String,
    onInfo: (String) -> Unit,
    onScannedDevicesUpdate: (List<String>) -> Unit,
    onRaspberryPiDevicesUpdate: (List<String>) -> Unit,
    onPeripheralStatesUpdate: (Map<String, Boolean>) -> Unit,
    onRemoteScannedDevicesUpdate: (List<String>) -> Unit
) {
    try {
        println("📨 解析JSON响应: $jsonData")

        val json = org.json.JSONObject(jsonData)
        val type = json.getString("type")

        println("📨 JSON响应类型: $type")
        onInfo("收到JSON响应: $type")

        when (type) {
            "scan_result" -> {
                if (json.has("devices")) {
                    val devicesArray = json.getJSONArray("devices")
                    val deviceList = mutableListOf<String>()

                    for (i in 0 until devicesArray.length()) {
                        val deviceJson = devicesArray.getJSONObject(i)
                        val mac = deviceJson.getString("mac")
                        val name = deviceJson.getString("name")
                        val rssi = deviceJson.optInt("rssi", -999)
                        val deviceType = deviceJson.optString("type", "01")

                        val formattedDevice = "$deviceType|$name|$mac"
                        deviceList.add(formattedDevice)

                        println("🔍 JSON扫描结果: 类型=$deviceType, 名称=$name, 地址=$mac, 信号强度=$rssi")
                        onInfo("✅ 发现设备: $name ($deviceType) 信号强度: $rssi")
                    }

                    // 更新扫描设备列表
                    onScannedDevicesUpdate(deviceList)
                    println("🔄 更新扫描设备列表，设备数量: ${deviceList.size}")

                    // 同时更新远程扫描设备列表（用于UI显示）
                    onRemoteScannedDevicesUpdate(deviceList)
                    println("🔄 更新远程扫描设备列表，设备数量: ${deviceList.size}")

                } else if (json.has("status")) {
                    val status = json.getString("status")
                    when (status) {
                        "ok" -> onInfo("✅ 扫描启动成功")
                        "fail" -> onInfo("❌ 扫描启动失败")
                        "stopped" -> onInfo("🛑 扫描已停止")
                    }
                }
            }

            "connect_result" -> {
                val status = json.getString("status")
                val mac = json.optString("mac", "")

                when (status) {
                    "ok" -> {
                        onInfo("✅ 设备连接成功: $mac")
                        // 可以在这里更新设备状态
                    }
                    "fail" -> {
                        onInfo("❌ 设备连接失败: $mac")
                    }
                }
            }

            "disconnect_result" -> {
                val status = json.getString("status")
                val mac = json.optString("mac", "")

                when (status) {
                    "ok" -> onInfo("✅ 设备断开成功: $mac")
                    else -> onInfo("❌ 设备断开失败: $mac")
                }
            }

            "error" -> {
                val reason = json.optString("reason", "未知错误")
                onInfo("❌ 错误: $reason")
                println("❌ JSON错误响应: $reason")
            }

            else -> {
                onInfo("⚠️ 未知JSON响应类型: $type")
                println("⚠️ 未知JSON响应类型: $type")
            }
        }

    } catch (e: Exception) {
        println("❌ 解析JSON响应时出错: ${e.message}")
        println("❌ 原始JSON数据: '$jsonData'")
        onInfo("❌ 解析JSON响应失败: ${e.message}")
    }
}

@Composable
private fun RaspberryPiControlSection(
    bluetoothManager: BluetoothManager,
    connectedDevices: List<BluetoothDeviceInfo>,
    scannedDevices: List<String>,
    remoteScannedDevices: List<String>,
    raspberryPiDevices: List<String>,
    peripheralStates: Map<String, Boolean>,
    keyBindings: Map<Int, String>,
    isScanning: Boolean,
    scanStatusMessage: String,
    onScannedDevicesUpdate: (List<String>) -> Unit,
    onRemoteScannedDevicesUpdate: (List<String>) -> Unit,
    onKeyBindingsUpdate: (Map<Int, String>) -> Unit,
    onScanStatusUpdate: (Boolean, String) -> Unit
) {
    var selectedRaspberryPi by remember { mutableStateOf<BluetoothDeviceInfo?>(null) }

    // 直接在这里管理树莓派扫描结果，简化状态管理
    var raspberryPiScanResults by remember { mutableStateOf<List<String>>(emptyList()) }

    // 监听蓝牙数据接收，直接处理树莓派扫描结果
    LaunchedEffect(bluetoothManager) {
        bluetoothManager.addCallback(object : BluetoothCallback {
            override fun onDataReceived(deviceAddress: String, data: String) {
                println("🔍 [直接监听] 收到数据: $data")

                // 简单直接的解析逻辑
                if (data.contains("A0:SCAN_RESULT:")) {
                    try {
                        val parts = data.trim().split(":")
                        if (parts.size >= 3) {
                            val deviceInfo = parts[2] // 格式: 类型|名称|地址|信号强度
                            val deviceParts = deviceInfo.split("|")
                            if (deviceParts.size >= 3) {
                                val deviceType = deviceParts[0]
                                val deviceName = deviceParts[1]
                                val deviceAddress = deviceParts[2]

                                val formattedDevice = "$deviceType|$deviceName|$deviceAddress"
                                println("🔍 [直接监听] 解析设备: $formattedDevice")

                                // 直接更新本地状态
                                raspberryPiScanResults = raspberryPiScanResults.toMutableList().apply {
                                    if (!any { it.contains(deviceAddress) }) {
                                        add(formattedDevice)
                                        println("🔍 [直接监听] 添加设备，当前列表大小: $size")
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        println("🔍 [直接监听] 解析失败: ${e.message}")
                    }
                }

                // 清空扫描结果的逻辑
                if (data.contains("A0:01:OK")) {
                    println("🔍 [直接监听] 开始扫描，清空结果")
                    raspberryPiScanResults = emptyList()
                }
            }

            override fun onDeviceFound(device: BluetoothDeviceInfo) {}
            override fun onError(error: String) {}
            override fun onSuccess(message: String) {}
            override fun onInfo(message: String) {}
            override fun onCharacteristicNotification(deviceAddress: String, characteristicUuid: String, data: ByteArray) {}
            override fun onPermissionRequired(permissions: Array<String>) {}
        })
    }

    // 按键绑定对话框状态
    var showBindingDialog by remember { mutableStateOf(false) }
    var selectedKeyForBinding by remember { mutableStateOf(0) }

    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "🍓 树莓派控制中心",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )

            // 选择树莓派设备
            Text("选择树莓派设备:")

            // 过滤出树莓派设备
            val filteredRaspberryPiDevices = connectedDevices.filter { device ->
                device.name?.startsWith(DeviceConfig.BluetoothDevicePrefix.raspberryPi) == true
            }

            // 调试信息：显示设备状态
            println("🔍 RaspberryPiControlSection 状态:")
            println("🔍 connectedDevices.size = ${connectedDevices.size}")
            connectedDevices.forEachIndexed { index, device ->
                println("🔍 连接设备 $index: ${device.name} (${device.address})")
            }
            println("🔍 filteredRaspberryPiDevices.size = ${filteredRaspberryPiDevices.size}")
            filteredRaspberryPiDevices.forEachIndexed { index, device ->
                println("🔍 树莓派设备 $index: ${device.name} (${device.address})")
            }
            println("🔍 selectedRaspberryPi = ${selectedRaspberryPi?.getDisplayName()}")
            println("🔍 DeviceConfig.BluetoothDevicePrefix.raspberryPi = ${DeviceConfig.BluetoothDevicePrefix.raspberryPi}")

            // 调试信息：只显示树莓派设备
            if (filteredRaspberryPiDevices.isNotEmpty()) {
                Text(
                    text = "可用的树莓派设备 (${filteredRaspberryPiDevices.size}个):",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Blue
                )
                filteredRaspberryPiDevices.forEach { device ->
                    Text(
                        text = "• ${device.getDisplayName()} (${device.address})",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Blue
                    )
                }
            } else {
                Text(
                    text = "未找到树莓派设备 (需要名称以 '${DeviceConfig.BluetoothDevicePrefix.raspberryPi}' 开头的设备)",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.Red
                )
            }

            if (filteredRaspberryPiDevices.isEmpty()) {
                Text(
                    text = "⚠️ 未找到树莓派设备 (需要名称前缀: ${DeviceConfig.BluetoothDevicePrefix.raspberryPi})",
                    color = Color.Red,
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = "请先连接到树莓派设备才能进行扫描操作",
                    color = Color.Gray,
                    style = MaterialTheme.typography.bodySmall
                )

                Text(
                    text = "💡 提示：树莓派控制中心需要连接到树莓派设备才能工作",
                    color = Color(0xFF1976D2),
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = "如需测试本地BLE扫描，请使用下方的BLE功能区域",
                    color = Color(0xFF1976D2),
                    style = MaterialTheme.typography.bodySmall
                )
            } else {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(filteredRaspberryPiDevices) { device ->
                        Button(
                            onClick = { selectedRaspberryPi = device },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = if (selectedRaspberryPi?.address == device.address)
                                    Color(0xFF4CAF50) else Color.Gray
                            )
                        ) {
                            Text(device.getDisplayName())
                        }
                    }
                }
            }

            // 调试信息：检查按钮区域可见性
            println("🔍 按钮区域检查: selectedRaspberryPi = ${selectedRaspberryPi?.getDisplayName()}")

            if (selectedRaspberryPi != null) {
                println("🔍 显示按钮区域")
                Divider()

                // 树莓派蓝牙控制
                Text(
                    text = "蓝牙设备管理",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                // 扫描状态显示
                if (scanStatusMessage.isNotEmpty()) {
                    Text(
                        text = "状态: $scanStatusMessage",
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (isScanning) Color(0xFF4CAF50) else Color(0xFF2196F3),
                        modifier = Modifier.padding(vertical = 4.dp)
                    )
                }

                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = {
                            println("🔍 点击开始扫描按钮")
                            println("🔍 当前 selectedRaspberryPi: ${selectedRaspberryPi?.getDisplayName()}")

                            // 不清空扫描结果，保持累积显示
                            // onScannedDevicesUpdate(emptyList()) // 注释掉这行

                            if (selectedRaspberryPi != null) {
                                // 设置扫描状态
                                onScanStatusUpdate(true, "正在启动扫描...")

                                // 支持JSON和传统格式
                                val useJsonFormat = false // 使用传统格式确保稳定性

                                val command = if (useJsonFormat) {
                                    // JSON格式指令
                                    """{"cmd":"scan_start","timeout":10}"""
                                } else {
                                    // 传统格式指令
                                    "A0:01"
                                }

                                bluetoothManager.sendData(selectedRaspberryPi!!.address, command)

                                // 添加调试信息
                                println("🔍 发送扫描指令到树莓派: ${selectedRaspberryPi!!.getDisplayName()} (${selectedRaspberryPi!!.address})")
                                println("🔍 发送的指令: $command")
                                println("🔍 指令格式: ${if (useJsonFormat) "JSON" else "传统"}")
                            } else {
                                println("❌ 未选择树莓派设备，无法发送扫描指令")
                                onScanStatusUpdate(false, "请先选择树莓派设备")
                            }

                            println("🔍 扫描指令发送完成")
                        }
                    ) {
                        Text("开始扫描")
                    }

                    Button(
                        onClick = {
                            // 树莓派控制中心：总是发送指令到远程树莓派
                            val command = "A0:00" // 0xA0=树莓派管理, 0x00=停止扫描
                            if (selectedRaspberryPi != null) {
                                onScanStatusUpdate(false, "正在停止扫描...")
                                bluetoothManager.sendData(selectedRaspberryPi!!.address, command)
                                println("🛑 发送停止扫描指令")
                            } else {
                                onScanStatusUpdate(false, "请先选择树莓派设备")
                            }
                        }
                    ) {
                        Text("停止扫描")
                    }

                    Button(
                        onClick = {
                            // 查询已连接设备
                            val command = "A2:00" // 0xA2=状态查询, 0x00=查询所有设备
                            if (selectedRaspberryPi != null) {
                                bluetoothManager.sendData(selectedRaspberryPi!!.address, command)
                            }
                        }
                    ) {
                        Text("刷新设备")
                    }

                    Button(
                        onClick = {
                            // 测试通信
                            val command = "TEST:PING" // 测试指令
                            if (selectedRaspberryPi != null) {
                                bluetoothManager.sendData(selectedRaspberryPi!!.address, command)
                            }
                        }
                    ) {
                        Text("测试通信")
                    }
                }

                // 测试按钮行
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = {
                            // 模拟接收到扫描结果，用于测试UI更新
                            println("🧪 模拟扫描结果测试")
                            val testDevices = listOf(
                                "02|测试充电器|AA:BB:CC:DD:EE:01",
                                "03|测试主灯|AA:BB:CC:DD:EE:02",
                                "99|测试设备|AA:BB:CC:DD:EE:03"
                            )
                            onScannedDevicesUpdate(testDevices)
                            println("🧪 测试数据已发送到UI")
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFFF9800)
                        )
                    ) {
                        Text("🧪 测试UI更新")
                    }

                    Button(
                        onClick = {
                            // 模拟树莓派返回扫描结果
                            println("🧪 模拟树莓派扫描响应")
                            val mockResponse = "A0:SCAN_RESULT:02|模拟充电器|AA:BB:CC:DD:EE:11"
                            println("🧪 模拟响应数据: $mockResponse")

                            // 直接调用响应处理函数
                            processRaspberryPiResponse(
                                mockResponse,
                                { info -> println("🧪 Info: $info") },
                                scannedDevices,
                                raspberryPiDevices,
                                peripheralStates,
                                remoteScannedDevices, // 使用真实的 remoteScannedDevices
                                onScannedDevicesUpdate,
                                { println("🧪 RaspberryPi devices updated") },
                                { println("🧪 Peripheral states updated") },
                                onRemoteScannedDevicesUpdate,
                                { scanning, message -> println("🧪 Scan status: $scanning, $message") }
                            )
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF9C27B0)
                        )
                    ) {
                        Text("🧪 模拟树莓派响应")
                    }

                    Button(
                        onClick = {
                            // 清空扫描结果
                            println("🧪 清空扫描结果")
                            onScannedDevicesUpdate(emptyList())
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF44336)
                        )
                    ) {
                        Text("清空结果")
                    }
                }

                // 显示树莓派连接的设备
                if (raspberryPiDevices.isNotEmpty()) {
                    Divider()
                    Text(
                        text = "已连接的外设",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    LazyColumn(
                        modifier = Modifier.heightIn(max = 200.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(raspberryPiDevices) { deviceInfo ->
                            val parts = deviceInfo.split("|")
                            if (parts.size >= 3) {
                                val deviceType = parts[0]
                                val deviceName = parts[1]
                                val deviceAddress = parts[2]
                                val isOn = peripheralStates[deviceAddress] ?: false

                                Card(
                                    colors = CardDefaults.cardColors(
                                        containerColor = if (isOn) Color(0xFF4CAF50).copy(alpha = 0.1f)
                                                        else MaterialTheme.colorScheme.surface
                                    )
                                ) {
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(12.dp),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Column {
                                            Text(
                                                text = getDeviceTypeIcon(deviceType) + " " + deviceName,
                                                fontWeight = FontWeight.Medium
                                            )
                                            Text(
                                                text = deviceAddress,
                                                style = MaterialTheme.typography.bodySmall,
                                                color = Color.Gray
                                            )
                                        }

                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            // 状态指示
                                            Text(
                                                text = if (isOn) "✅" else "⭕",
                                                fontSize = 16.sp
                                            )

                                            // 开关按钮
                                            Button(
                                                onClick = {
                                                    val command = "A1:${deviceType}:${if (isOn) "00" else "01"}:$deviceAddress"
                                                    bluetoothManager.sendData(selectedRaspberryPi!!.address, command)
                                                },
                                                colors = ButtonDefaults.buttonColors(
                                                    containerColor = if (isOn) Color(0xFFFF5722) else Color(0xFF4CAF50)
                                                )
                                            ) {
                                                Text(if (isOn) "关闭" else "开启")
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 按键绑定功能
                if (raspberryPiDevices.isNotEmpty()) {
                    Divider()
                    Text(
                        text = "🎮 蓝牙按键绑定",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Text(
                        text = "将物理按键绑定到外设设备，按键操作时可直接控制对应设备",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.Gray
                    )

                    // 6个按键的绑定设置
                    LazyColumn(
                        modifier = Modifier.heightIn(max = 300.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(6) { keyIndex ->
                            val keyNumber = keyIndex + 1
                            val boundDeviceAddress = keyBindings[keyNumber]
                            val boundDevice = if (boundDeviceAddress != null) {
                                raspberryPiDevices.find { it.contains(boundDeviceAddress) }
                            } else null

                            Card(
                                colors = CardDefaults.cardColors(
                                    containerColor = if (boundDevice != null) Color(0xFF4CAF50).copy(alpha = 0.1f) else Color.Gray.copy(alpha = 0.1f)
                                )
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(12.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Column {
                                        Text(
                                            text = "按键 $keyNumber",
                                            fontWeight = FontWeight.Medium
                                        )
                                        if (boundDevice != null) {
                                            val parts = boundDevice.split("|")
                                            if (parts.size >= 3) {
                                                val deviceType = parts[0]
                                                val deviceName = parts[1]
                                                Text(
                                                    text = "${getDeviceTypeIcon(deviceType)} $deviceName",
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = Color(0xFF4CAF50)
                                                )
                                            }
                                        } else {
                                            Text(
                                                text = "未绑定",
                                                style = MaterialTheme.typography.bodySmall,
                                                color = Color.Gray
                                            )
                                        }
                                    }

                                    Row(
                                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        // 绑定按钮
                                        Button(
                                            onClick = {
                                                // 显示设备选择对话框
                                                selectedKeyForBinding = keyNumber
                                                showBindingDialog = true
                                            },
                                            colors = ButtonDefaults.buttonColors(
                                                containerColor = Color(0xFF2196F3)
                                            )
                                        ) {
                                            Text(if (boundDevice != null) "重新绑定" else "绑定")
                                        }

                                        // 解绑按钮
                                        if (boundDevice != null) {
                                            Button(
                                                onClick = {
                                                    onKeyBindingsUpdate(keyBindings.toMutableMap().apply {
                                                        remove(keyNumber)
                                                    })
                                                },
                                                colors = ButtonDefaults.buttonColors(
                                                    containerColor = Color(0xFFFF5722)
                                                )
                                            ) {
                                                Text("解绑")
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 显示树莓派扫描结果区域（选择了树莓派时显示）
        if (selectedRaspberryPi != null) {
            Divider()
            Text(
                text = "🍓 树莓派扫描结果",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            // 调试信息：显示当前扫描设备状态
            println("🔍 [UI渲染] selectedRaspberryPi = ${selectedRaspberryPi?.getDisplayName()}")
            println("🔍 [UI渲染] raspberryPiScanResults.size = ${raspberryPiScanResults.size}")
            raspberryPiScanResults.forEachIndexed { index, device ->
                println("🔍 [UI渲染] raspberryPiScanResults[$index] = $device")
            }

            // 直接使用本地状态作为显示数据源
            val displayDevices = raspberryPiScanResults
            println("🔍 [UI渲染] 使用 raspberryPiScanResults 作为显示数据，设备数量: ${displayDevices.size}")

            // 强制触发重组以确保UI更新
            LaunchedEffect(displayDevices.size) {
                println("🔄 [UI重组] displayDevices 大小变化触发重组: ${displayDevices.size}")
            }

            if (displayDevices.isNotEmpty()) {
                println("🔍 [UI显示] 准备显示 ${displayDevices.size} 个设备")
                Text(
                    text = "找到 ${displayDevices.size} 个设备:",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF4CAF50)
                )

                LazyColumn(
                    modifier = Modifier.heightIn(max = 150.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(displayDevices) { deviceInfo ->
                        println("🔍 [UI项目] 渲染设备项: $deviceInfo")
                        val parts = deviceInfo.split("|")
                        if (parts.size >= 3) {
                            val deviceType = parts[0]
                            val deviceName = parts[1]
                            val deviceAddress = parts[2]
                            println("🔍 [UI项目] 解析设备: 类型=$deviceType, 名称=$deviceName, 地址=$deviceAddress")

                            Card(
                                colors = CardDefaults.cardColors(
                                    containerColor = Color(0xFF2196F3).copy(alpha = 0.1f)
                                )
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(12.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Column {
                                        Text(
                                            text = getDeviceTypeIcon(deviceType) + " " + deviceName,
                                            fontWeight = FontWeight.Medium
                                        )
                                        Text(
                                            text = deviceAddress,
                                            style = MaterialTheme.typography.bodySmall,
                                            color = Color.Gray
                                        )
                                    }

                                    Button(
                                        onClick = {
                                            // 发送连接指令到选中的树莓派
                                            val command = "A0:CONNECT:$deviceAddress"
                                            bluetoothManager.sendData(selectedRaspberryPi!!.address, command)
                                            println("🔗 发送连接指令: $command 到树莓派: ${selectedRaspberryPi!!.address}")
                                        },
                                        colors = ButtonDefaults.buttonColors(
                                            containerColor = Color(0xFF2196F3)
                                        )
                                    ) {
                                        Text("连接")
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                println("🔍 [UI显示] 没有扫描设备显示")
                println("🔍 [UI显示] raspberryPiScanResults.size = ${raspberryPiScanResults.size}")
                Text(
                    text = "暂无扫描结果，请点击'开始扫描'按钮\n(树莓派扫描结果: ${raspberryPiScanResults.size})",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.Gray
                )
            }
        }
    }

    // 按键绑定对话框
    if (showBindingDialog) {
        AlertDialog(
            onDismissRequest = { showBindingDialog = false },
            title = { Text("绑定按键 $selectedKeyForBinding") },
            text = {
                Column {
                    Text("选择要绑定到按键 $selectedKeyForBinding 的设备：")
                    Spacer(modifier = Modifier.height(16.dp))

                    LazyColumn(
                        modifier = Modifier.heightIn(max = 300.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(raspberryPiDevices) { deviceInfo ->
                            val parts = deviceInfo.split("|")
                            if (parts.size >= 3) {
                                val deviceType = parts[0]
                                val deviceName = parts[1]
                                val deviceAddress = parts[2]

                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .clickable {
                                            // 绑定设备到按键
                                            onKeyBindingsUpdate(keyBindings.toMutableMap().apply {
                                                this[selectedKeyForBinding] = deviceAddress
                                            })
                                            showBindingDialog = false
                                        },
                                    colors = CardDefaults.cardColors(
                                        containerColor = Color(0xFF2196F3).copy(alpha = 0.1f)
                                    )
                                ) {
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(12.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = "${getDeviceTypeIcon(deviceType)} $deviceName",
                                            fontWeight = FontWeight.Medium
                                        )
                                        Spacer(modifier = Modifier.weight(1f))
                                        Text(
                                            text = deviceAddress,
                                            style = MaterialTheme.typography.bodySmall,
                                            color = Color.Gray
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showBindingDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

// 获取设备类型图标
private fun getDeviceTypeIcon(deviceType: String): String {
    return when (deviceType) {
        "02" -> "🔌" // 无线充电器
        "03" -> "💡" // Lamp主灯
        "04" -> "🌈" // 氛围灯
        "05" -> "🌸" // 香氛机
        "06" -> "🌀" // 风扇
        else -> "📱" // 其他设备
    }
}

@Composable
private fun BleControlSection(
    bluetoothManager: BluetoothManager
) {
    var isBleScanningState by remember { mutableStateOf(false) }
    var connectedBleDevices by remember { mutableStateOf<List<String>>(emptyList()) }

    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "BLE (蓝牙低功耗) 功能",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )

            // BLE扫描控制
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = {
                        if (isBleScanningState) {
                            bluetoothManager.stopBleScan()
                            isBleScanningState = false
                        } else {
                            if (bluetoothManager.startBleScan()) {
                                isBleScanningState = true
                            }
                        }
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text(if (isBleScanningState) "停止BLE扫描" else "开始BLE扫描")
                }
            }

            // 连接到BLE设备示例
            Text(
                text = "BLE设备连接示例",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )

            var deviceAddress by remember { mutableStateOf("") }

            OutlinedTextField(
                value = deviceAddress,
                onValueChange = { deviceAddress = it },
                label = { Text("BLE设备地址") },
                placeholder = { Text("例如: AA:BB:CC:DD:EE:FF") },
                modifier = Modifier.fillMaxWidth()
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = {
                        if (deviceAddress.isNotBlank()) {
                            bluetoothManager.connectToBleDevice(deviceAddress.trim())
                        }
                    },
                    modifier = Modifier.weight(1f),
                    enabled = deviceAddress.isNotBlank()
                ) {
                    Text("连接BLE设备")
                }

                Button(
                    onClick = {
                        if (deviceAddress.isNotBlank()) {
                            bluetoothManager.subscribeToFFE4Notification(deviceAddress.trim())
                        }
                    },
                    modifier = Modifier.weight(1f),
                    enabled = deviceAddress.isNotBlank()
                ) {
                    Text("订阅0xFFE4")
                }
            }

            // 快速连接按钮
            Button(
                onClick = {
                    if (deviceAddress.isNotBlank()) {
                        bluetoothManager.autoConnectAndSubscribeBleDevice(deviceAddress.trim())
                    }
                },
                modifier = Modifier.fillMaxWidth(),
                enabled = deviceAddress.isNotBlank(),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF4CAF50))
            ) {
                Text("快速连接并订阅 (一键完成)", color = Color.White)
            }

            // 诊断按钮组
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = {
                        if (deviceAddress.isNotBlank()) {
                            bluetoothManager.diagnoseBleDevice(deviceAddress.trim())
                        }
                    },
                    modifier = Modifier.weight(1f),
                    enabled = deviceAddress.isNotBlank(),
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFF9800))
                ) {
                    Text("设备诊断", color = Color.White)
                }

                Button(
                    onClick = {
                        if (deviceAddress.isNotBlank()) {
                            bluetoothManager.diagnoseBleConnectionIssues(deviceAddress.trim())
                        }
                    },
                    modifier = Modifier.weight(1f),
                    enabled = deviceAddress.isNotBlank(),
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFE91E63))
                ) {
                    Text("连接诊断", color = Color.White)
                }
            }

            // 显示已连接的BLE设备
            LaunchedEffect(Unit) {
                connectedBleDevices = bluetoothManager.getConnectedBleDevices()
            }

            if (connectedBleDevices.isNotEmpty()) {
                Text(
                    text = "已连接的BLE设备:",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )

                connectedBleDevices.forEach { address ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = address,
                            fontSize = 12.sp,
                            modifier = Modifier.weight(1f)
                        )

                        Button(
                            onClick = {
                                bluetoothManager.disconnectBleDevice(address)
                                connectedBleDevices = bluetoothManager.getConnectedBleDevices()
                            },
                            colors = ButtonDefaults.buttonColors(containerColor = Color.Red)
                        ) {
                            Text("断开", color = Color.White)
                        }
                    }
                }
            }

            // 使用说明
            Text(
                text = """
                📖 使用说明:
                1. 输入BLE设备地址 (格式: AA:BB:CC:DD:EE:FF)
                2. 点击"快速连接并订阅"一键完成连接和订阅
                3. 或分步操作：先"连接BLE设备"，再"订阅0xFFE4"
                4. 接收到的按键信号将显示在上方消息列表中

                🔧 故障排除:
                • 连接失败：点击"连接诊断"查看详细原因
                • 找不到特征值：点击"设备诊断"检查设备兼容性
                • GATT错误133：通常是信号问题，请靠近设备重试
                """.trimIndent(),
                fontSize = 12.sp,
                color = Color.Gray,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

@Composable
private fun UnifiedBluetoothServerSection(
    bluetoothManager: BluetoothManager,
    isServerRunning: Boolean,
    isUnifiedServer: Boolean,
    receivedMessages: List<ReceivedMessage>,
    onStartClassicServer: () -> Unit,
    onStartUnifiedServer: () -> Unit,
    onStopServer: () -> Unit,
    onClearMessages: () -> Unit
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = if (DeviceConfig.isRaspberryPiVersion) "蓝牙服务器（接收方）" else "蓝牙服务器",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(DeviceConfig.UI.primaryColor)
            )

            // 服务器状态显示
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = when {
                        !isServerRunning -> "服务器状态: 未启动"
                        isUnifiedServer -> "服务器状态: 统一服务器运行中"
                        else -> "服务器状态: 经典蓝牙服务器运行中"
                    },
                    fontSize = 14.sp
                )

                if (isServerRunning) {
                    Text(
                        text = "●",
                        color = Color.Green,
                        fontSize = 20.sp
                    )
                }
            }

            // 服务器控制按钮（根据配置显示）
            if (DeviceConfig.Features.showServerControls) {
                if (!isServerRunning) {
                    Column(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = onStartUnifiedServer,
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF4CAF50))
                        ) {
                            Text("启动统一蓝牙服务器 (推荐)", color = Color.White)
                        }

                        Text(
                            text = "统一服务器可同时接收经典蓝牙和BLE设备消息",
                            fontSize = 12.sp,
                            color = Color.Gray,
                            modifier = Modifier.padding(horizontal = 8.dp)
                        )

                        Button(
                            onClick = onStartClassicServer,
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF2196F3))
                        ) {
                            Text("启动经典蓝牙服务器", color = Color.White)
                        }
                    }
                } else {
                    Button(
                        onClick = onStopServer,
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(containerColor = Color.Red)
                    ) {
                        Text("停止服务器", color = Color.White)
                    }
                }
            } else if (isServerRunning) {
                // 即使不显示控制按钮，也要显示服务器状态
                Text(
                    text = "服务器已自动启动并运行中",
                    fontSize = 14.sp,
                    color = Color(0xFF4CAF50),
                    fontWeight = FontWeight.Medium
                )
            }

            // 消息列表
            if (receivedMessages.isNotEmpty()) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "接收到的消息 (${receivedMessages.size})",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )

                    Button(
                        onClick = onClearMessages,
                        colors = ButtonDefaults.buttonColors(containerColor = Color.Gray)
                    ) {
                        Text("清空", color = Color.White)
                    }
                }

                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // 最新消息在最上面：反转列表顺序
                    items(receivedMessages.takeLast(50).reversed()) { message ->
                        MessageItemWithTime(message = message)
                    }
                }
            }

            // 使用说明
            Text(
                text = if (isUnifiedServer) {
                    "统一服务器正在运行，可以接收:\n• 经典蓝牙设备的SPP消息\n• BLE设备的特征值通知(如0xFFE4按键信号)"
                } else if (isServerRunning) {
                    "经典蓝牙服务器正在运行，只能接收SPP协议消息"
                } else {
                    "选择服务器类型:\n• 统一服务器: 同时支持经典蓝牙和BLE\n• 经典服务器: 仅支持经典蓝牙SPP协议"
                },
                fontSize = 12.sp,
                color = Color.Gray,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

@Composable
fun MessageItemWithTime(message: ReceivedMessage) {
    val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    val timeString = timeFormat.format(Date(message.timestamp))

    Card(
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "来自: ${message.senderAddress}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
                Text(
                    text = timeString,
                    fontSize = 12.sp,
                    color = Color.Gray,
                    fontWeight = FontWeight.Medium
                )
            }
            Text(
                text = message.data,
                fontSize = 14.sp,
                modifier = Modifier.padding(top = 2.dp)
            )
        }
    }
}
