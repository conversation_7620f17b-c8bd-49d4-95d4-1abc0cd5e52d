package com.example.exhibition_car_control.config

/**
 * 简单设备配置
 *
 * 🔧 使用方法：
 * 1. 修改下面的 DEVICE_TYPE 配置
 * 2. 在Android Studio中运行应用
 *
 * 📱 PHONE = 手机版（发送方）- 显示扫描、连接、发送功能
 * 🖥️ RASPBERRY_PI = 树莓派版（接收方）- 显示服务器、接收功能
 */
object DeviceConfig {

    // ⭐ 在这里修改设备类型 ⭐
    private val DEVICE_TYPE = DeviceType.PHONE  // 手机版本 - 用于控制树莓派

    enum class DeviceType {
        PHONE,           // 手机版（发送方）
        RASPBERRY_PI     // 树莓派版（接收方）
    }

    // 当前设备类型
    val currentDeviceType: DeviceType = DEVICE_TYPE

    // 是否为手机版
    val isPhoneVersion: Boolean = (DEVICE_TYPE == DeviceType.PHONE)

    // 是否为树莓派版
    val isRaspberryPiVersion: Boolean = (DEVICE_TYPE == DeviceType.RASPBERRY_PI)
    
    // 功能开关 - 根据设备类型自动配置
    object Features {
        val showBluetoothClient: Boolean = true                  // 两个版本都显示客户端功能
        val showBluetoothServer: Boolean = isRaspberryPiVersion  // 显示服务器功能（接收消息）
        val showDeviceScanning: Boolean = true                   // 两个版本都显示设备扫描
        val showDataSending: Boolean = isPhoneVersion            // 显示数据发送（只有手机版发送数据到树莓派）
        val showSilentConnection: Boolean = isRaspberryPiVersion // 显示静默连接控制（只有树莓派版需要）
        val showBleFeatures: Boolean = isRaspberryPiVersion      // 显示BLE功能（只有树莓派版需要连接按键设备）
        val autoStartServer: Boolean = isRaspberryPiVersion      // 自动启动服务器
        val showServerControls: Boolean = isRaspberryPiVersion   // 显示服务器控制按钮
        val showRaspberryPiControl: Boolean = isPhoneVersion     // 显示树莓派控制功能（只在手机端显示）
    }

    // 蓝牙设备名称前缀配置
    object BluetoothDevicePrefix {
        val raspberryPi: String = "Luban"        // 树莓派设备前缀
        val charger: String = "SMART-WC"                // 充电器设备前缀 (设备类型02)
        val mainLight: String = "SMART-ML"            // 主灯设备前缀 (设备类型03)
        val ambientLight: String = "SMART-AL"      // 氛围灯设备前缀 (设备类型04)
        val aromatherapy: String = "SMART-DF"      // 香薰设备前缀 (设备类型05)
        val fan: String = "SMART-FN"                        // 风扇设备前缀 (设备类型06)
        val bluetoothKey: String = "Dotix"      // 蓝牙按键设备前缀 (设备类型01)
    }

    // UI配置
    object UI {
        val mainTitle: String = if (isPhoneVersion) "蓝牙控制端" else "蓝牙接收端"
        val subtitle: String = if (isPhoneVersion) "发送控制指令到其他设备" else "接收并处理控制指令"
        val primaryColor: Long = if (isPhoneVersion) 0xFF2196F3 else 0xFF4CAF50  // 蓝色/绿色
    }
}
