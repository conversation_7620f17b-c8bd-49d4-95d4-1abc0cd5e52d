package com.example.exhibition_car_control

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.zerosense.bluetooth.BluetoothCallback
import com.zerosense.bluetooth.BluetoothDeviceInfo
import com.zerosense.bluetooth.BluetoothManager

/**
 * 扫描设备数据类 - 移到函数外部避免类型解析问题
 */
data class ScanDevice(
    val name: String,
    val address: String,
    val rssi: String,
    val type: String,
    val isConnecting: Boolean = false,
    val connectionStatus: String = ""
)

/**
 * 全新的树莓派扫描控制界面
 * 简化架构，直接管理状态，避免复杂的回调链
 */
@Composable
fun NewRaspberryPiScanScreen(
    bluetoothManager: BluetoothManager,
    connectedDevices: List<BluetoothDeviceInfo>
) {
    // 本地状态管理
    var selectedRaspberryPi by remember { mutableStateOf<BluetoothDeviceInfo?>(null) }
    var scanResults by remember { mutableStateOf(emptyList<ScanDevice>()) }
    var isScanning by remember { mutableStateOf(false) }
    var scanStatus by remember { mutableStateOf("准备扫描") }
    var lastMessage by remember { mutableStateOf("") }
    
    // 数据监听器
    DisposableEffect(bluetoothManager) {
        val callback = object : BluetoothCallback {
            override fun onDataReceived(deviceAddress: String, data: String) {
                println("📡 [Dotix扫描界面] 收到数据: '$data'")
                lastMessage = "最新消息: $data"
                
                val cleanData = data.trim()
                
                when {
                    // 开始扫描确认
                    cleanData.contains("A0:01:OK") -> {
                        println("✅ [Luban扫描界面] 树莓派确认开始扫描")
                        isScanning = true
                        scanResults = emptyList() // 清空之前的结果
                        scanStatus = "树莓派正在扫描..."
                    }

                    // 停止扫描确认
                    cleanData.contains("A0:00:OK") -> {
                        println("🛑 [Luban扫描界面] 树莓派确认停止扫描")
                        isScanning = false
                        scanStatus = "扫描已停止"
                    }

                    // 扫描完成 - 树莓派主动通知扫描结束并返回所有结果
                    cleanData.contains("A0:SCAN_COMPLETE:") -> {
                        println("🏁 [Luban扫描界面] 树莓派扫描完成，收到结果")
                        isScanning = false

                        try {
                            // 解析格式: A0:SCAN_COMPLETE:device1,name1,addr1,rssi1|device2,name2,addr2,rssi2|...
                            val parts = cleanData.split(":", limit = 3)
                            if (parts.size >= 3) {
                                val devicesData = parts[2]
                                if (devicesData.isNotEmpty() && devicesData != "EMPTY") {
                                    val deviceList = devicesData.split("|")
                                    val newScanResults = mutableListOf<ScanDevice>()

                                    deviceList.forEach { deviceInfo ->
                                        if (deviceInfo.isNotEmpty()) {
                                            val deviceParts = deviceInfo.split(",")
                                            if (deviceParts.size >= 3) {
                                                val deviceType = deviceParts[0]
                                                val deviceName = deviceParts[1]
                                                val deviceAddress = deviceParts[2]
                                                val rssi = if (deviceParts.size >= 4) deviceParts[3] else "未知"

                                                newScanResults.add(ScanDevice(
                                                    name = deviceName,
                                                    address = deviceAddress,
                                                    rssi = rssi,
                                                    type = deviceType
                                                ))

                                                println("📱 [Luban扫描界面] 发现设备: $deviceName ($deviceAddress)")
                                            }
                                        }
                                    }

                                    scanResults = newScanResults
                                    scanStatus = "树莓派扫描完成，共找到 ${scanResults.size} 个设备"
                                    println("✅ [Luban扫描界面] 树莓派扫描完成，共 ${scanResults.size} 个设备")
                                } else {
                                    scanResults = emptyList()
                                    scanStatus = "树莓派扫描完成，未找到设备"
                                    println("ℹ️ [Luban扫描界面] 树莓派扫描完成，未发现任何设备")
                                }
                            }
                        } catch (e: Exception) {
                            println("❌ [Luban扫描界面] 解析树莓派扫描结果失败: ${e.message}")
                            scanStatus = "扫描结果解析失败"
                        }
                    }

                    // 连接响应
                    cleanData.contains("A0:CONNECT_RESULT:") -> {
                        println("🔗 [Luban扫描界面] 收到连接结果")
                        try {
                            // 解析格式: A0:CONNECT_RESULT:deviceAddress:SUCCESS/FAILED:message
                            val parts = cleanData.split(":", limit = 5)
                            if (parts.size >= 4) {
                                val deviceAddress = parts[2]
                                val result = parts[3]
                                val message = if (parts.size >= 5) parts[4] else ""

                                // 更新对应设备的连接状态
                                scanResults = scanResults.map { device ->
                                    if (device.address == deviceAddress) {
                                        device.copy(
                                            isConnecting = false,
                                            connectionStatus = if (result == "SUCCESS") "连接成功" else "连接失败: $message"
                                        )
                                    } else {
                                        device
                                    }
                                }

                                println("✅ [Luban扫描界面] 设备 $deviceAddress 连接结果: $result")
                            }
                        } catch (e: Exception) {
                            println("❌ [Luban扫描界面] 解析连接结果失败: ${e.message}")
                        }
                    }
                }
            }
            
            override fun onDeviceFound(device: BluetoothDeviceInfo) {}
            override fun onError(error: String) { 
                lastMessage = "错误: $error"
                isScanning = false
                scanStatus = "扫描出错: $error"
            }
            override fun onSuccess(message: String) { lastMessage = "成功: $message" }
            override fun onInfo(message: String) { lastMessage = "信息: $message" }
            override fun onCharacteristicNotification(deviceAddress: String, characteristicUuid: String, data: ByteArray) {}
            override fun onPermissionRequired(permissions: Array<String>) {}
        }
        
        bluetoothManager.addCallback(callback)
        onDispose {
            bluetoothManager.removeCallback(callback)
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        Text(
            text = "🔍 Luban设备扫描控制",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold
        )
        
        // 树莓派选择
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "选择Luban设备",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                val raspberryPiDevices = connectedDevices.filter {
                    it.name?.startsWith("Luban", ignoreCase = true) == true
                }
                
                if (raspberryPiDevices.isEmpty()) {
                    Text(
                        text = "未找到已连接的Luban设备",
                        color = Color.Gray
                    )
                } else {
                    raspberryPiDevices.forEach { device ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = selectedRaspberryPi?.address == device.address,
                                onClick = { selectedRaspberryPi = device }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(text = "${device.name} (${device.address})")
                        }
                    }
                }
            }
        }
        
        // 扫描控制
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "扫描控制",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                // 状态显示
                Text(
                    text = "状态: $scanStatus",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (isScanning) Color.Blue else Color.Gray
                )
                
                // 控制按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = {
                            selectedRaspberryPi?.let { device ->
                                println("🔍 [Luban扫描界面] 发送开始扫描指令给树莓派")
                                bluetoothManager.sendData(device.address, "A0:01")
                                scanStatus = "已发送扫描指令，等待树莓派响应..."
                            }
                        },
                        enabled = selectedRaspberryPi != null && !isScanning
                    ) {
                        Text("开始扫描")
                    }

                    Button(
                        onClick = {
                            selectedRaspberryPi?.let { device ->
                                println("🛑 [Luban扫描界面] 发送停止扫描指令给树莓派")
                                bluetoothManager.sendData(device.address, "A0:00")
                                scanStatus = "已发送停止指令，等待树莓派响应..."
                            }
                        },
                        enabled = selectedRaspberryPi != null && isScanning
                    ) {
                        Text("停止扫描")
                    }
                }
            }
        }
        
        // 扫描结果
        Card(
            modifier = Modifier.fillMaxWidth().weight(1f)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "扫描结果 (${scanResults.size})",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                if (scanResults.isEmpty()) {
                    Text(
                        text = "暂无扫描结果",
                        color = Color.Gray,
                        modifier = Modifier.padding(16.dp)
                    )
                } else {
                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(scanResults) { device ->
                            Card(
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                                )
                            ) {
                                Column(
                                    modifier = Modifier.padding(12.dp)
                                ) {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Column(
                                            modifier = Modifier.weight(1f)
                                        ) {
                                            Text(
                                                text = device.name,
                                                fontWeight = FontWeight.Bold
                                            )
                                            Text(
                                                text = "地址: ${device.address}",
                                                style = MaterialTheme.typography.bodySmall
                                            )
                                            Text(
                                                text = "信号强度: ${device.rssi} dBm",
                                                style = MaterialTheme.typography.bodySmall
                                            )
                                            Text(
                                                text = "类型: ${device.type}",
                                                style = MaterialTheme.typography.bodySmall
                                            )

                                            // 连接状态显示
                                            if (device.connectionStatus.isNotEmpty()) {
                                                Text(
                                                    text = device.connectionStatus,
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = if (device.connectionStatus.contains("成功"))
                                                        Color.Green else Color.Red,
                                                    fontWeight = FontWeight.Bold
                                                )
                                            }
                                        }

                                        // 连接按钮
                                        Button(
                                            onClick = {
                                                selectedRaspberryPi?.let { raspberryPi ->
                                                    println("🔗 [Luban扫描界面] 发送连接请求: ${device.address}")
                                                    bluetoothManager.sendData(raspberryPi.address, "A0:CONNECT:${device.address}")

                                                    // 更新设备连接状态
                                                    scanResults = scanResults.map {
                                                        if (it.address == device.address) {
                                                            it.copy(isConnecting = true, connectionStatus = "连接中...")
                                                        } else {
                                                            it
                                                        }
                                                    }
                                                }
                                            },
                                            enabled = selectedRaspberryPi != null && !device.isConnecting,
                                            modifier = Modifier.size(width = 80.dp, height = 36.dp)
                                        ) {
                                            if (device.isConnecting) {
                                                CircularProgressIndicator(
                                                    modifier = Modifier.size(16.dp),
                                                    strokeWidth = 2.dp
                                                )
                                            } else {
                                                Text(
                                                    text = "连接",
                                                    fontSize = 12.sp
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // 调试信息
        if (lastMessage.isNotEmpty()) {
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
                )
            ) {
                Text(
                    text = lastMessage,
                    modifier = Modifier.padding(8.dp),
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}
